
'''

这个文件和src_profile_register.py类似， 但是的格式是xyz


这个程序的目的是是先粗匹配， 然后再精匹配
1 点云， 模板和场景一起显示
2 选择点， 场景和模板对应的点
3 完成粗匹配和精匹配

'''


import open3d as o3d
import numpy as np
import copy
import matplotlib.pyplot as plt
import logging
import os

import sys



def pick_points(pcd):
    """
    手动选择点云中的点
    返回选择的点的索引
    """
    print("请选择4个点 (按Shift+左键选择点, Q退出)")
    vis = o3d.visualization.VisualizerWithEditing()
    vis.create_window()
    vis.add_geometry(pcd)
    vis.run()  # 用户交互选点
    vis.destroy_window()
    return vis.get_picked_points()


def save_init_matrix(matrix, filename):
    
    # Add _keypoint before the extension
    filename_parts = filename.rsplit('.', 1)
    filename_parts[1] = ".txt"
    filename = filename_parts[0] + '_init_trans_matrix' + filename_parts[1]
    print("filename: ", filename)
    np.savetxt(filename, matrix, fmt='%.6f')
   

def manual_registration(source, target):
    """
    手动选择对应点进行初始配准
    """
    print("在源点云中选择4个特征点 (按Shift+左键选择点, Q退出):")
    source_picked_idx = pick_points(source)
    print("在目标点云中选择对应的4个特征点 (按Shift+左键选择点, Q退出):")
    target_picked_idx = pick_points(target)

    print(f"source_picked_idx: {source_picked_idx}, target_picked_idx: {target_picked_idx}")

    # 验证选择的点数量
    if len(source_picked_idx) < 4 or len(target_picked_idx) < 4:
        raise ValueError("必须至少选择4个对应点")

    if len(source_picked_idx) != len(target_picked_idx):
        raise ValueError("源点云和目标点云的选择点数量必须相同")

    # 只使用前4个点
    source_picked_idx = source_picked_idx[:4]
    target_picked_idx = target_picked_idx[:4]

    # 获取选择的点的坐标
    source_points = np.asarray(source.points)[source_picked_idx]
    target_points = np.asarray(target.points)[target_picked_idx]
    

    print(source_points)
    print(target_points)   
    
    
    # 使用正确的Kabsch算法求解刚体变换
    center_source = np.mean(source_points, axis=0)
    center_target = np.mean(target_points, axis=0)
    source_centered = source_points - center_source
    target_centered = target_points - center_target

    # 正确的Kabsch算法：H = target^T * source
    H = np.dot(target_centered.T, source_centered)
    U, S, Vt = np.linalg.svd(H)
    R = np.dot(U, Vt)

    # 确保R是旋转矩阵（行列式为1）
    if np.linalg.det(R) < 0:
        print("检测到反射，修正为旋转矩阵")
        Vt[-1, :] *= -1
        R = np.dot(U, Vt)

    # 计算平移向量：t = target_center - R * source_center
    t = center_target - np.dot(R, center_source)
    trans_init = np.eye(4)
    trans_init[:3,:3] = R
    trans_init[:3,3] = t

    print(trans_init)   
    # 显示源点云和目标点云中选择的对应点
    source_points_pcd = o3d.geometry.PointCloud()
    target_points_pcd = o3d.geometry.PointCloud()
    
    source_points_pcd.points = o3d.utility.Vector3dVector(source_points)
    target_points_pcd.points = o3d.utility.Vector3dVector(target_points)

    print('disp source_points_pcd')
    o3d.visualization.draw_geometries([source_points_pcd, target_points_pcd]) 

    source_points_pcd_trans = copy.deepcopy(source_points_pcd)
    source_points_pcd_trans.transform(trans_init)
    # 设置点的颜色和大小
    source_points_pcd_trans.paint_uniform_color([1, 0, 0])  # 红色
    target_points_pcd.paint_uniform_color([0, 1, 0])  # 绿色
    
    # 可视化选择的对应点
    o3d.visualization.draw_geometries([source_points_pcd_trans, target_points_pcd])
    
    return trans_init

def preprocess_pointcloud(pcd, voxel_size=0.05, remove_outliers=True):
    """
    点云预处理：体素滤波、离群点去除、法向量估计
    """
    print(f"  预处理点云 (原始点数: {len(pcd.points)})")

    # 体素滤波降采样
    if voxel_size > 0:
        pcd = pcd.voxel_down_sample(voxel_size)
        print(f"  体素滤波后: {len(pcd.points)} 点")

    # 去除离群点
    if remove_outliers:
        pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
        print(f"  去除离群点后: {len(pcd.points)} 点")

    # 估计法向量
    pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=0.5, max_nn=30)
    )

    return pcd


def z_constrained_optimization(source, target, transformation, max_iterations=50):
    """
    Z方向约束优化：专门优化z方向的配准精度
    """
    print("  执行Z方向约束优化...")

    source_points = np.asarray(source.points)
    target_points = np.asarray(target.points)

    # 应用当前变换
    source_transformed = copy.deepcopy(source)
    source_transformed.transform(transformation)
    source_transformed_points = np.asarray(source_transformed.points)

    # 构建KD树用于快速搜索
    target_tree = o3d.geometry.KDTreeFlann(target)

    best_transformation = transformation.copy()
    best_z_error = float('inf')

    # 迭代优化z方向偏移
    for iteration in range(max_iterations):
        z_errors = []

        # 计算当前z方向误差
        for point in source_transformed_points:
            [k, idx, _] = target_tree.search_knn_vector_3d(point, 1)
            if k > 0:
                target_point = target_points[idx[0]]
                z_error = abs(point[2] - target_point[2])
                z_errors.append(z_error)

        if len(z_errors) == 0:
            break

        current_z_error = np.mean(z_errors)

        if current_z_error < best_z_error:
            best_z_error = current_z_error
            best_transformation = transformation.copy()

        # 如果z误差已经很小，提前退出
        if current_z_error < 0.05:  # 0.05mm
            break

        # 计算z方向的平均偏移
        z_offset = np.mean([target_points[target_tree.search_knn_vector_3d(point, 1)[1][0]][2] - point[2]
                           for point in source_transformed_points[:min(1000, len(source_transformed_points))]])

        # 应用小的z方向调整
        z_adjustment = np.eye(4)
        z_adjustment[2, 3] = z_offset * 0.1  # 小步长调整

        transformation = np.dot(z_adjustment, transformation)
        source_transformed.transform(z_adjustment)
        source_transformed_points = np.asarray(source_transformed.points)

    print(f"    Z方向优化完成，最终Z误差: {best_z_error:.4f}mm")
    return best_transformation


def multi_scale_icp_registration(source, target, trans_init,
                                max_correspondence_distances=[0.5, 0.2, 0.1, 0.05],
                                use_z_constraint=True):
    """
    多尺度ICP配准：从粗到精的配准策略，包含z方向约束优化
    """
    print("应用多尺度ICP配准")
    current_transformation = trans_init

    for i, max_dist in enumerate(max_correspondence_distances):
        print(f"  第{i+1}阶段配准 (对应距离阈值: {max_dist}mm)")

        # 设置更严格的收敛条件用于精细配准
        if max_dist <= 0.1:
            relative_fitness = 1e-6
            relative_rmse = 1e-6
            max_iteration = 200
        else:
            relative_fitness = 1e-4
            relative_rmse = 1e-4
            max_iteration = 100

        reg_result = o3d.pipelines.registration.registration_icp(
            source, target, max_dist, current_transformation,
            o3d.pipelines.registration.TransformationEstimationPointToPoint(),
            o3d.pipelines.registration.ICPConvergenceCriteria(
                relative_fitness=relative_fitness,
                relative_rmse=relative_rmse,
                max_iteration=max_iteration
            )
        )

        current_transformation = reg_result.transformation
        print(f"    Fitness: {reg_result.fitness:.6f}, RMSE: {reg_result.inlier_rmse:.6f}")

        # 在最后一个阶段添加z方向约束优化
        if use_z_constraint and i == len(max_correspondence_distances) - 1:
            current_transformation = z_constrained_optimization(source, target, current_transformation)

    return current_transformation


def icp_registration(source, target, trans_init, max_correspondence_distance=0.05,
                    use_preprocessing=True, use_multiscale=True):
    """
    改进的ICP配准：支持预处理和多尺度配准
    """
    print("开始改进的ICP配准")

    # 预处理点云
    if use_preprocessing:
        print("预处理源点云...")
        source_processed = preprocess_pointcloud(copy.deepcopy(source),
                                                voxel_size=0.02, remove_outliers=True)
        print("预处理目标点云...")
        target_processed = preprocess_pointcloud(copy.deepcopy(target),
                                                voxel_size=0.02, remove_outliers=True)
    else:
        source_processed = source
        target_processed = target

    # 选择配准策略
    if use_multiscale:
        # 多尺度配准，启用z方向约束优化
        transformation = multi_scale_icp_registration(
            source_processed, target_processed, trans_init,
            max_correspondence_distances=[0.5, 0.2, 0.1, max_correspondence_distance],
            use_z_constraint=True
        )
    else:
        # 单尺度精细配准
        print(f"应用单尺度ICP配准 (对应距离阈值: {max_correspondence_distance}mm)")
        reg_result = o3d.pipelines.registration.registration_icp(
            source_processed, target_processed, max_correspondence_distance, trans_init,
            o3d.pipelines.registration.TransformationEstimationPointToPoint(),
            o3d.pipelines.registration.ICPConvergenceCriteria(
                relative_fitness=1e-6,      # 更严格的收敛条件
                relative_rmse=1e-6,         # 更严格的收敛条件
                max_iteration=200))         # 增加迭代次数

        transformation = reg_result.transformation
        print(f"ICP配准结果 - Fitness: {reg_result.fitness:.6f}, RMSE: {reg_result.inlier_rmse:.6f}")

    return transformation


def save_plt(filename):
    
    filename_parts = filename.rsplit('.', 1)
    filename_parts[1] = ".jpg"
    filename = filename_parts[0] + '_result'

    plt.savefig(filename)




def distance_near_point_sick(model, source, savename='x.jpg'):
    dist = source.compute_point_cloud_distance(model) # 2025年2月26日11:26:07， 这条语句才是以source为基准

    n_model = len(model.points)
    n_source = len(source.points)
    n_dist = len(dist)
    # print(f'n_model: {n_model}, n_source: {n_source}, n_dist: {n_dist}')
    
    idx= [i for i,distance in enumerate(dist)  if distance<1.0 ]
    N = len(idx)
    # print(f'N: {N}')
   
    filter_ratio = (1 - N/n_source)*100
    # print(f'filter_ratio: {filter_ratio}%')
    
    if N<1:
        logging.info('没有相邻的点')
        return
    #最后将点云中相同的部分和不同的部分分别取出来进行显示
    same_part = source.select_by_index(idx)
    diff_part = source.select_by_index(idx,invert=True)
    same_part.paint_uniform_color([0,1,0])
    diff_part.paint_uniform_color([1,0,0])  

    model_tree =  o3d.geometry.KDTreeFlann(model)
    

    a_left = np.zeros((N,3)) # model 的点   
    a_right = np.zeros((N,3)) # source 的点
    distance = np.zeros(N)

  
    for i in range(N):
        index2 = idx[i]
        point2 = source.points[index2]

        a_right[i,:] =  point2  # 参考基准  以右边为准
        k = 1  # 只去搜索一个
        [k, Index, _] = model_tree.search_knn_vector_3d(point2,k)    
        index1 = Index[0]  # 
        point1 = model.points[index1]  
        a_left[i,:] = point1
        distance[i] = np.linalg.norm(point1 - point2)

    
    source_points = np.asarray(source.points)
    target_points = np.asarray(model.points)


    # 统计距离大于0.2的点个数
    N_far = len([i for i,distance in enumerate(distance)  if distance>0.2 ])
    # print(f'距离大于0.2的点个数为：{N_far}, 比例为：{N_far/N}')
    # 分别以x z轴显示相邻点的距离
    x,y,z = a_left[:,0], a_left[:,1], a_left[:,2]    


    # 在同一个figure中显示两个子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左侧子图显示点云投影（修正为真正的X-Z投影）
    ax1.scatter(source_points[:, 0], source_points[:, 2], c='red', label='Source', alpha=0.6, s=1)
    ax1.scatter(target_points[:, 0], target_points[:, 2], c='green', label='Target', alpha=0.6, s=1)
    ax1.set_xlabel('X axis')
    ax1.set_ylabel('Z axis')
    ax1.set_title('Point Cloud X-Z Projection')
    ax1.legend()
    ax1.grid(True)

    # 右侧子图显示距离误差
    ax2.plot(x, distance, 'b.')
    ax2.set_title(f'filter_ratio = {filter_ratio:.2f}% error(< 0.2mm) percent={100-100.0*N_far/N:.2f}%')
    ax2.grid(True)

    print(f'percent={100-100.0*N_far/N:.2f}%')

    plt.tight_layout()    
    save_plt(savename)
    plt.show()

 


def load_init_matrix(filename):
    # Add _init_trans_matrix before the extension
    base, ext = os.path.splitext(filename)
    filename = base + '_init_trans_matrix' + '.txt'
    print("filename: ", filename)
    return np.loadtxt(filename)

def main():       
    

    gen_init_matrix = False  # True: 鼠标手动选择轮廓与标轨对应的特征点

    file_idx = 2  # 数据组别
    color_str = 'blue'  #轮廓以颜色区分
    filename_xyz = f'{color_str}_filtered.xyz'

    path =  f"./20250730/{file_idx}/"
    
    filename_trans_init = f'{color_str}_filtered_init_trans_matrix.txt'

    source_filename = path + filename_xyz
    points_xyz = np.loadtxt(source_filename)
    source = o3d.geometry.PointCloud()
    source.points = o3d.utility.Vector3dVector(points_xyz)

    
    model_filename = 'rail-3.xyz'
    points_xyz = np.loadtxt(model_filename)
    target = o3d.geometry.PointCloud()
    target.points = o3d.utility.Vector3dVector(points_xyz)
    


    # 可视化原始点云
    source.paint_uniform_color([1, 0, 0])  # 红色
    target.paint_uniform_color([0, 1, 0])  # 绿色
    # o3d.visualization.draw_geometries([source, target])  
        
    
    
    # 手动选择对应点进行初始配准
    init_matrix_filename = copy.copy(source_filename)

    if gen_init_matrix:
        trans_init = manual_registration(source, target)
        save_init_matrix(trans_init, init_matrix_filename)
        sys.exit()
    

    
    trans_init = np.loadtxt(path + filename_trans_init)
    source_temp = copy.deepcopy(source)
    source_temp.transform(trans_init)
    print("初始配准完成")
    # o3d.visualization.draw_geometries([source_temp, target])
    
    # ICP精细配准
    trans_icp = icp_registration(source, target, trans_init)
    source_temp = copy.deepcopy(source)
    source_temp.transform(trans_icp)
    print("ICP配准完成")
    
    # 可视化最终结果
    # o3d.visualization.draw_geometries([source_temp, target])

    save_plt_name = copy.copy(source_filename)
    

    distance_near_point_sick(target,source_temp, save_plt_name)
    


   

if __name__ == "__main__":
    main()