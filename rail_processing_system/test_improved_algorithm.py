#!/usr/bin/env python3
"""
测试改进后的算法效果
验证z方向误差是否降低到0.1mm以内
"""

import os
import sys
import numpy as np
import time
from datetime import datetime

# 导入改进后的模块
from process_rail_data import process_rail_data
from config import get_config

def test_z_error_improvement():
    """
    测试z方向误差改进效果
    """
    print("=" * 80)
    print("测试改进后的点云配准算法")
    print("目标：z方向误差平均值和标准差都降低到0.1mm以内")
    print("=" * 80)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据文件
    config = get_config()
    rail_dir = config['data']['rail_data_dir']
    
    print(f"\n检查数据文件 (目录: {rail_dir}):")
    data_files = ['blue.xyz', 'green.xyz', 'red.xyz']
    for filename in data_files:
        filepath = os.path.join(rail_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"  ✓ {filename} ({size:,} bytes)")
        else:
            print(f"  ✗ {filename} (不存在)")
            return False
    
    print(f"\n当前配置参数:")
    print(f"  ICP对应距离阈值: {config['processing']['icp_max_correspondence_distance']}mm")
    print(f"  拼接误差距离阈值: {config['processing']['distance_threshold']}mm")
    print(f"  高精度阈值: {config['quality']['high_precision_threshold']}mm")
    print(f"  启用预处理: {config['algorithm']['use_preprocessing']}")
    print(f"  启用多尺度配准: {config['algorithm']['use_multiscale']}")
    print(f"  启用z方向约束: {config['algorithm']['use_z_constraint']}")
    
    # 运行改进后的处理程序
    print(f"\n开始运行改进后的处理程序...")
    start_time = time.time()
    
    try:
        results = process_rail_data()
        end_time = time.time()
        
        print(f"处理完成 (耗时: {end_time - start_time:.1f}秒)")
        
        # 分析结果
        analyze_results(results)
        
        return True
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_results(results):
    """
    分析处理结果，检查z方向误差是否达到目标
    """
    print(f"\n" + "=" * 60)
    print("结果分析")
    print("=" * 60)
    
    target_mean_error = 0.1  # 目标平均误差
    target_std_error = 0.1   # 目标标准差
    
    pairs = ['blue_vs_green', 'blue_vs_red', 'green_vs_red']
    
    all_results_meet_target = True
    
    for pair in pairs:
        if pair in results:
            result = results[pair]
            
            print(f"\n{pair.upper()} 配准结果:")
            print("-" * 40)
            
            # 配准质量
            if 'registration' in result:
                reg_stats = result['registration']
                print(f"配准统计:")
                print(f"  总点数: {reg_stats.get('total_points', 'N/A'):,}")
                print(f"  高精度点(<0.1mm): {reg_stats.get('very_close_points', 'N/A'):,} ({reg_stats.get('very_close_ratio', 0):.1f}%)")
                print(f"  中精度点(<0.3mm): {reg_stats.get('close_points', 'N/A'):,} ({reg_stats.get('close_ratio', 0):.1f}%)")
                print(f"  平均距离: {reg_stats.get('mean_distance', 'N/A'):.4f} mm")
            
            # 拼接误差分析
            if 'stitching' in result:
                stitch_stats = result['stitching']
                mean_z_error = stitch_stats.get('mean_z_error', float('inf'))
                std_z_error = stitch_stats.get('std_z_error', float('inf'))
                
                print(f"拼接误差:")
                print(f"  匹配点对: {stitch_stats.get('matched_pairs', 'N/A'):,}")
                if stitch_stats.get('outliers_removed', 0) > 0:
                    print(f"  移除异常值: {stitch_stats.get('outliers_removed', 0):,}")
                print(f"  平均Z误差: {mean_z_error:.4f} mm")
                print(f"  Z误差标准差: {std_z_error:.4f} mm")
                print(f"  最大Z误差: {stitch_stats.get('max_z_error', 'N/A'):.4f} mm")
                print(f"  Z误差<0.1mm占比: {stitch_stats.get('z_error_under_0_1_ratio', 0)*100:.1f}%")
                print(f"  Z误差<0.3mm占比: {stitch_stats.get('z_error_under_0_3_ratio', 0)*100:.1f}%")
                
                # 检查是否达到目标
                mean_meets_target = mean_z_error <= target_mean_error
                std_meets_target = std_z_error <= target_std_error
                
                print(f"\n目标达成情况:")
                print(f"  平均误差 ≤ {target_mean_error}mm: {'✓' if mean_meets_target else '✗'} ({mean_z_error:.4f}mm)")
                print(f"  标准差 ≤ {target_std_error}mm: {'✓' if std_meets_target else '✗'} ({std_z_error:.4f}mm)")
                
                if not (mean_meets_target and std_meets_target):
                    all_results_meet_target = False
    
    # 总结
    print(f"\n" + "=" * 60)
    print("总体评估")
    print("=" * 60)
    
    if all_results_meet_target:
        print("🎉 恭喜！所有配准对的z方向误差都达到了目标要求！")
        print(f"✓ 平均误差和标准差都控制在{target_mean_error}mm以内")
        print("✓ 算法改进成功")
    else:
        print("⚠️  部分配准对未达到目标要求")
        print("建议进一步优化算法参数或增加更多约束条件")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """
    主函数
    """
    success = test_z_error_improvement()
    
    if success:
        print(f"\n📊 详细结果文件位置:")
        print(f"  - 变换矩阵: ../rail_results/*_transformation_matrix.txt")
        print(f"  - 配准统计: ../rail_results/*_registration_stats.txt")
        print(f"  - 拼接误差: ../rail_results/*_stitching_error.txt")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
