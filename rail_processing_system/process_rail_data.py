#!/usr/bin/env python3
"""
处理rail文件夹中的三组新数据
使用修正后的算法进行点云配准和误差分析
"""

import open3d as o3d
import numpy as np
import copy
import matplotlib.pyplot as plt
import os
import sys
import time
from datetime import datetime

# 导入修正后的函数
from srs_profile_register_format_xyz import icp_registration
from srs_stitching_error import calculate_stitching_error

def load_and_filter_pointcloud(filename, z_range=None, max_points=None):
    """
    加载并过滤点云数据
    """
    print(f"加载点云: {filename}")
    
    # 加载点云数据
    points = np.loadtxt(filename)
    print(f"  原始点数: {len(points)}")
    
    # 如果数据有4列，只取前3列(XYZ)
    if points.shape[1] > 3:
        points = points[:, :3]
    
    # Z值过滤
    if z_range:
        z_min, z_max = z_range
        z_mask = (points[:, 2] >= z_min) & (points[:, 2] <= z_max)
        points = points[z_mask]
        print(f"  Z值过滤后: {len(points)} 点 (范围: [{z_min}, {z_max}])")
    
    # 点数限制（用于大数据集的处理效率）
    if max_points and len(points) > max_points:
        # 随机采样
        indices = np.random.choice(len(points), max_points, replace=False)
        points = points[indices]
        print(f"  随机采样后: {len(points)} 点")
    
    return points

def register_pointclouds(source_points, target_points, output_dir, pair_name):
    """
    执行点云配准
    """
    print(f"\n=== 配准点云对: {pair_name} ===")
    
    # 创建点云对象
    source = o3d.geometry.PointCloud()
    source.points = o3d.utility.Vector3dVector(source_points)
    
    target = o3d.geometry.PointCloud()
    target.points = o3d.utility.Vector3dVector(target_points)
    
    # 设置颜色
    source.paint_uniform_color([1, 0, 0])  # 红色
    target.paint_uniform_color([0, 1, 0])  # 绿色
    
    # 初始配准（使用单位矩阵作为初始变换）
    trans_init = np.eye(4)
    
    print("执行改进的ICP配准...")
    start_time = time.time()

    # 使用改进的ICP配准：启用预处理和多尺度配准
    transformation = icp_registration(source, target, trans_init,
                                    max_correspondence_distance=0.03,  # 更严格的阈值
                                    use_preprocessing=True,
                                    use_multiscale=True)

    end_time = time.time()

    print(f"改进ICP配准完成 (耗时: {end_time - start_time:.2f}秒)")

    # 应用变换
    source_transformed = copy.deepcopy(source)
    source_transformed.transform(transformation)

    # 计算最终配准质量
    distances = source_transformed.compute_point_cloud_distance(target)
    distances = np.asarray(distances)
    fitness = len(distances[distances < 0.05]) / len(distances)  # 5cm内的点比例
    rmse = np.sqrt(np.mean(distances**2))

    print(f"  最终Fitness: {fitness:.6f}")
    print(f"  最终RMSE: {rmse:.6f} mm")

    # 保存变换矩阵
    matrix_file = os.path.join(output_dir, f"{pair_name}_transformation_matrix.txt")
    np.savetxt(matrix_file, transformation, fmt='%.8f')
    print(f"变换矩阵已保存: {matrix_file}")

    # 创建一个模拟的reg_result对象以保持兼容性
    class RegResult:
        def __init__(self, transformation, fitness, rmse):
            self.transformation = transformation
            self.fitness = fitness
            self.inlier_rmse = rmse

    reg_result = RegResult(transformation, fitness, rmse)

    return source_transformed, target, reg_result

def analyze_registration_accuracy(source_transformed, target, output_dir, pair_name):
    """
    分析配准精度
    """
    print(f"分析配准精度...")
    
    # 计算点到点距离
    distances = source_transformed.compute_point_cloud_distance(target)
    distances = np.asarray(distances)
    
    # 统计分析
    close_points = distances[distances < 1.0]  # 距离小于1mm的点
    very_close_points = distances[distances < 0.2]  # 距离小于0.2mm的点
    
    stats = {
        'total_points': len(distances),
        'close_points': len(close_points),
        'very_close_points': len(very_close_points),
        'close_ratio': len(close_points) / len(distances) * 100,
        'very_close_ratio': len(very_close_points) / len(distances) * 100,
        'mean_distance': np.mean(distances),
        'max_distance': np.max(distances),
        'min_distance': np.min(distances),
        'std_distance': np.std(distances)
    }
    
    print(f"配准精度分析:")
    print(f"  总点数: {stats['total_points']}")
    print(f"  距离<1.0mm: {stats['close_points']} ({stats['close_ratio']:.1f}%)")
    print(f"  距离<0.2mm: {stats['very_close_points']} ({stats['very_close_ratio']:.1f}%)")
    print(f"  平均距离: {stats['mean_distance']:.4f} mm")
    print(f"  最大距离: {stats['max_distance']:.4f} mm")
    
    # 保存统计结果
    stats_file = os.path.join(output_dir, f"{pair_name}_registration_stats.txt")
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write(f"点云配准精度分析报告\n")
        f.write(f"====================\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"点云对: {pair_name}\n")
        f.write(f"--------------------\n")
        f.write(f"总点数: {stats['total_points']}\n")
        f.write(f"距离<1.0mm的点数: {stats['close_points']} ({stats['close_ratio']:.2f}%)\n")
        f.write(f"距离<0.2mm的点数: {stats['very_close_points']} ({stats['very_close_ratio']:.2f}%)\n")
        f.write(f"平均距离: {stats['mean_distance']:.6f} mm\n")
        f.write(f"最大距离: {stats['max_distance']:.6f} mm\n")
        f.write(f"最小距离: {stats['min_distance']:.6f} mm\n")
        f.write(f"距离标准差: {stats['std_distance']:.6f} mm\n")
    
    print(f"统计结果已保存: {stats_file}")
    return stats

def calculate_stitching_errors(points1, points2, output_dir, pair_name):
    """
    计算拼接误差（改进版）
    """
    print(f"计算拼接误差...")

    # 创建点云对象
    pcd1 = o3d.geometry.PointCloud()
    pcd1.points = o3d.utility.Vector3dVector(points1)

    pcd2 = o3d.geometry.PointCloud()
    pcd2.points = o3d.utility.Vector3dVector(points2)

    # 计算拼接误差，使用更严格的参数和异常值过滤
    output_file = os.path.join(output_dir, f"{pair_name}_stitching_error.txt")

    try:
        error_stats = calculate_stitching_error(pcd1, pcd2,
                                              distance_threshold=0.3,  # 更严格的距离阈值
                                              output_file=output_file,
                                              remove_outliers=True,    # 启用异常值过滤
                                              outlier_method='iqr')    # 使用IQR方法
        print(f"拼接误差分析完成，结果已保存: {output_file}")
        return error_stats
    except Exception as e:
        print(f"拼接误差分析失败: {e}")
        return None

def process_rail_data():
    """
    处理rail文件夹中的三组数据
    """
    print("=" * 60)
    print("处理rail文件夹中的三组新数据")
    print("=" * 60)
    
    # 创建输出目录
    output_dir = "rail_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 数据文件路径
    data_files = {
        'blue': 'rail/blue.xyz',
        'green': 'rail/green.xyz', 
        'red': 'rail/red.xyz'
    }
    
    # 检查数据范围
    print("\n检查数据范围...")
    data_ranges = {}
    for color, filename in data_files.items():
        points = np.loadtxt(filename)[:, :3]  # 只取XYZ
        z_min, z_max = np.min(points[:, 2]), np.max(points[:, 2])
        data_ranges[color] = (z_min, z_max, len(points))
        print(f"{color}: Z范围[{z_min:.2f}, {z_max:.2f}], 点数: {len(points)}")
    
    # 确定公共Z范围
    all_z_mins = [r[0] for r in data_ranges.values()]
    all_z_maxs = [r[1] for r in data_ranges.values()]
    common_z_min = max(all_z_mins)
    common_z_max = min(all_z_maxs)
    
    print(f"\n公共Z范围: [{common_z_min:.2f}, {common_z_max:.2f}]")
    
    # 加载和过滤数据
    print("\n加载和过滤数据...")
    filtered_data = {}
    max_points_per_cloud = 50000  # 限制点数以提高处理效率
    
    for color, filename in data_files.items():
        filtered_data[color] = load_and_filter_pointcloud(
            filename, 
            z_range=(common_z_min, common_z_max),
            max_points=max_points_per_cloud
        )
    
    # 定义要处理的点云对
    pairs = [
        ('blue', 'green'),
        ('blue', 'red'), 
        ('green', 'red')
    ]
    
    # 处理每个点云对
    results = {}
    for source_color, target_color in pairs:
        pair_name = f"{source_color}_vs_{target_color}"
        print(f"\n{'='*50}")
        print(f"处理点云对: {pair_name}")
        print(f"{'='*50}")
        
        source_points = filtered_data[source_color]
        target_points = filtered_data[target_color]
        
        # 1. 点云配准
        source_transformed, target, reg_result = register_pointclouds(
            source_points, target_points, output_dir, pair_name
        )
        
        # 2. 配准精度分析
        reg_stats = analyze_registration_accuracy(
            source_transformed, target, output_dir, pair_name
        )
        
        # 3. 拼接误差分析
        source_transformed_points = np.asarray(source_transformed.points)
        target_points_array = np.asarray(target.points)
        
        stitching_stats = calculate_stitching_errors(
            source_transformed_points, target_points_array, output_dir, pair_name
        )
        
        results[pair_name] = {
            'registration': reg_stats,
            'stitching': stitching_stats,
            'icp_result': reg_result
        }
    
    return results

def main():
    """
    主函数
    """
    print("开始处理rail文件夹中的新数据...")
    
    try:
        results = process_rail_data()
        
        print("\n" + "="*60)
        print("所有数据处理完成！")
        print("="*60)
        
        # 打印汇总结果
        print("\n📊 处理结果汇总:")
        print("-" * 50)
        
        for pair_name, result in results.items():
            reg_stats = result['registration']
            icp_result = result['icp_result']
            
            print(f"\n{pair_name}:")
            print(f"  ICP Fitness: {icp_result.fitness:.6f}")
            print(f"  ICP RMSE: {icp_result.inlier_rmse:.6f} mm")
            print(f"  高精度点占比: {reg_stats['very_close_ratio']:.1f}% (<0.2mm)")
            print(f"  平均距离: {reg_stats['mean_distance']:.4f} mm")
        
        print(f"\n所有结果文件已保存到: rail_results/ 目录")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
