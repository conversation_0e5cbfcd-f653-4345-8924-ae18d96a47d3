#!/usr/bin/env python3
"""
Rail数据处理系统使用示例
演示如何使用系统的各个组件
"""

import os
import sys
import numpy as np
from config import get_config

# 导入核心模块
from srs_profile_register_format_xyz import icp_registration
from srs_stitching_error import calculate_stitching_error

def example_basic_usage():
    """
    基本使用示例
    """
    print("=" * 60)
    print("基本使用示例")
    print("=" * 60)
    
    # 1. 加载配置
    config = get_config()
    print("✓ 配置加载完成")
    
    # 2. 检查数据文件
    rail_dir = config['data']['rail_data_dir']
    data_files = config['data']['data_files']
    
    print(f"\n检查数据文件 (目录: {rail_dir}):")
    for color, filename in data_files.items():
        filepath = os.path.join(rail_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"  ✓ {color}: {filename} ({size:,} bytes)")
        else:
            print(f"  ✗ {color}: {filename} (不存在)")

def example_single_pair_processing():
    """
    单个点云对处理示例
    """
    print("\n" + "=" * 60)
    print("单个点云对处理示例")
    print("=" * 60)
    
    config = get_config()
    
    # 模拟加载两个点云
    print("模拟点云数据...")
    
    # 创建示例点云数据
    np.random.seed(42)
    
    # 源点云 (蓝色)
    source_points = np.random.randn(1000, 3) * 10 + [100, 100, 150]
    
    # 目标点云 (红色) - 添加小的变换
    transformation = np.array([
        [0.99, -0.01, 0.02, 1.0],
        [0.01, 0.99, -0.01, 0.5],
        [-0.02, 0.01, 0.99, -0.2],
        [0, 0, 0, 1]
    ])
    
    # 应用变换到源点云生成目标点云
    source_homo = np.hstack([source_points, np.ones((len(source_points), 1))])
    target_homo = source_homo @ transformation.T
    target_points = target_homo[:, :3]
    
    # 添加噪声
    target_points += np.random.randn(*target_points.shape) * 0.1
    
    print(f"  源点云: {len(source_points)} 点")
    print(f"  目标点云: {len(target_points)} 点")
    
    # 执行配准 (这里简化处理，实际使用时需要创建Open3D点云对象)
    print("\n执行配准...")
    print("  (示例中使用模拟数据，实际使用时会调用ICP算法)")
    
    # 模拟配准结果
    print("  模拟配准结果:")
    print("    Fitness: 0.95")
    print("    RMSE: 0.12 mm")
    print("    高精度点占比: 85.2%")

def example_batch_processing():
    """
    批量处理示例
    """
    print("\n" + "=" * 60)
    print("批量处理示例")
    print("=" * 60)
    
    # 定义要处理的点云对
    pairs = [
        ('blue', 'green'),
        ('blue', 'red'),
        ('green', 'red')
    ]
    
    print("批量处理点云对:")
    for i, (source, target) in enumerate(pairs, 1):
        print(f"\n{i}. {source.upper()} vs {target.upper()}")
        print("   - 加载点云数据")
        print("   - 执行Z值滤波")
        print("   - 随机采样到50,000点")
        print("   - ICP配准")
        print("   - 拼接误差分析")
        print("   - 保存结果文件")

def example_custom_parameters():
    """
    自定义参数示例
    """
    print("\n" + "=" * 60)
    print("自定义参数示例")
    print("=" * 60)
    
    # 获取默认配置
    config = get_config()
    
    print("默认ICP参数:")
    icp_config = config['processing']
    print(f"  对应距离阈值: {icp_config['icp_max_correspondence_distance']} mm")
    print(f"  收敛阈值(fitness): {icp_config['icp_relative_fitness']}")
    print(f"  收敛阈值(RMSE): {icp_config['icp_relative_rmse']}")
    print(f"  最大迭代次数: {icp_config['icp_max_iteration']}")
    
    print("\n自定义参数示例:")
    custom_params = {
        'icp_max_correspondence_distance': 0.3,  # 更严格的距离阈值
        'icp_max_iteration': 200,                # 更多迭代次数
        'max_points_per_cloud': 100000,         # 更多采样点
    }
    
    for param, value in custom_params.items():
        print(f"  {param}: {value}")
    
    print("\n使用自定义参数的方法:")
    print("  1. 修改 config.py 文件中的默认值")
    print("  2. 在代码中动态修改配置字典")
    print("  3. 创建自定义配置文件")

def example_result_analysis():
    """
    结果分析示例
    """
    print("\n" + "=" * 60)
    print("结果分析示例")
    print("=" * 60)
    
    # 模拟配准结果
    results = {
        'blue_vs_red': {
            'fitness': 0.698,
            'rmse': 0.184,
            'high_precision_ratio': 52.1,
            'mean_error': 0.127
        },
        'blue_vs_green': {
            'fitness': 0.347,
            'rmse': 0.291,
            'high_precision_ratio': 10.4,
            'mean_error': 0.099
        },
        'green_vs_red': {
            'fitness': 0.056,
            'rmse': 0.154,
            'high_precision_ratio': 4.7,
            'mean_error': 0.081
        }
    }
    
    print("配准结果分析:")
    
    # 按质量排序
    sorted_results = sorted(results.items(), 
                          key=lambda x: x[1]['high_precision_ratio'], 
                          reverse=True)
    
    for i, (pair, result) in enumerate(sorted_results, 1):
        print(f"\n{i}. {pair.replace('_', '-').upper()}")
        print(f"   Fitness: {result['fitness']:.3f}")
        print(f"   RMSE: {result['rmse']:.3f} mm")
        print(f"   高精度点占比: {result['high_precision_ratio']:.1f}%")
        print(f"   平均误差: {result['mean_error']:.3f} mm")
    
    # 质量评估
    best_pair = sorted_results[0]
    worst_pair = sorted_results[-1]
    
    print(f"\n质量评估:")
    print(f"  最佳配准: {best_pair[0].replace('_', '-').upper()}")
    print(f"  最差配准: {worst_pair[0].replace('_', '-').upper()}")
    print(f"  质量差异: {best_pair[1]['high_precision_ratio'] - worst_pair[1]['high_precision_ratio']:.1f}%")

def example_error_handling():
    """
    错误处理示例
    """
    print("\n" + "=" * 60)
    print("错误处理示例")
    print("=" * 60)
    
    print("常见错误及处理方法:")
    
    errors = [
        {
            'error': 'FileNotFoundError: rail数据文件不存在',
            'solution': '检查rail/目录下是否有blue.xyz, green.xyz, red.xyz文件'
        },
        {
            'error': 'MemoryError: 内存不足',
            'solution': '减少max_points_per_cloud参数值，如改为30000或20000'
        },
        {
            'error': 'ICP配准失败',
            'solution': '调整ICP参数，增加max_correspondence_distance或max_iteration'
        },
        {
            'error': '点云数据格式错误',
            'solution': '确保XYZ文件格式正确，每行包含X Y Z坐标'
        }
    ]
    
    for i, error_info in enumerate(errors, 1):
        print(f"\n{i}. {error_info['error']}")
        print(f"   解决方案: {error_info['solution']}")

def main():
    """
    主函数 - 运行所有示例
    """
    print("深瑞视相机Rail数据处理系统 - 使用示例")
    
    try:
        example_basic_usage()
        example_single_pair_processing()
        example_batch_processing()
        example_custom_parameters()
        example_result_analysis()
        example_error_handling()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        print("=" * 60)
        print("\n快速开始:")
        print("1. 确保rail/目录下有数据文件")
        print("2. 运行: python run_rail_processing.py")
        print("3. 查看结果: ../rail_results/目录")
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        print("请检查系统环境和依赖项")

if __name__ == "__main__":
    main()
